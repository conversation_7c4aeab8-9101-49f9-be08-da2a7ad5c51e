# 🚀 RAG 智能问答系统

<div align="center">

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![LangChain](https://img.shields.io/badge/LangChain-0.2+-green.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)
![Status](https://img.shields.io/badge/Status-Production-brightgreen.svg)

**一个企业级的检索增强生成(RAG)智能问答系统**

支持多存储架构 | 动态上下文压缩 | 混合检索策略 | 中文优化

[快速开始](#-快速开始) • [功能特性](#-核心功能) • [技术架构](#-技术架构) • [部署指南](#-部署指南) • [API 文档](#-api使用)

</div>

---

## 📋 项目概述

RAG 智能问答系统是一个基于最新 AI 技术的企业级知识库问答解决方案。系统采用先进的检索增强生成技术，结合多存储架构、动态上下文压缩和混合检索策略，为用户提供准确、智能的问答服务。

### 🌟 核心特性

#### 🏗️ 企业级架构

- **多存储系统**: Redis + MySQL + Elasticsearch + ChromaDB
- **混合检索**: ES 关键词粗排 + 向量语义精排
- **动态压缩**: 基于 Transformers 的上下文智能压缩
- **会话管理**: 完整的对话历史和会话状态管理
- **系统监控**: 全方位的健康检查和性能监控

#### 🧠 智能优化

- **中文分词**: JiebaTokenizer 精准中文分词
- **查询扩展**: 同义词扩展提升检索召回率
- **上下文压缩**: 动态 Token 优化减少成本
- **智能回退**: 多级回退机制保证系统稳定性
- **自适应检索**: 根据查询类型选择最优检索策略

#### 🔧 开发友好

- **一键部署**: 完整的 Docker 和本地部署方案
- **配置统一**: 集中化配置管理
- **API 丰富**: RESTful API 和 Python SDK
- **测试完善**: 全面的单元测试和集成测试
- **文档详细**: 完整的开发和部署文档

## 🚀 快速开始

### 📋 环境要求

| 组件       | 要求  | 说明               |
| ---------- | ----- | ------------------ |
| **Python** | 3.8+  | 推荐 3.9+          |
| **内存**   | 8GB+  | 推荐 16GB+         |
| **存储**   | 10GB+ | 模型和数据存储     |
| **GPU**    | 可选  | 支持 CUDA/MPS 加速 |

### ⚡ 一键安装

```bash
# 克隆项目
git clone https://github.com/your-repo/rag-system.git
cd rag-system/rag

# 安装依赖
pip install -r requirements.txt

# 设置API密钥
export DASHSCOPE_API_KEY="your_dashscope_api_key"

# 系统检查
python check.py

# 快速体验
python test_core_enhancements.py
```

### 🔧 详细安装步骤

#### 1. 环境准备

```bash
# 创建虚拟环境（推荐）
python -m venv rag_env
source rag_env/bin/activate  # Linux/Mac
# rag_env\Scripts\activate  # Windows

# 升级pip
pip install --upgrade pip
```

#### 2. 安装依赖

```bash
# 安装核心依赖
pip install -r requirements.txt

# 验证安装
python -c "import torch, transformers, langchain; print('✅ 核心依赖安装成功')"
```

#### 3. 服务部署（可选）

```bash
# 启动Redis（如果需要）
redis-server

# 启动MySQL（如果需要）
mysql.server start

# 启动Elasticsearch（如果需要）
elasticsearch
```

#### 4. 数据库初始化

```bash
# 创建MySQL数据库
mysql -u root -p < up.sql

# 验证数据库
mysql -u root -p -e "USE rag_system; SHOW TABLES;"
```

### 🎯 快速体验

#### 方式一：自动测试

```bash
# 运行完整功能测试
python test_enhanced_features.py

# 运行核心功能测试
python test_core_enhancements.py
```

#### 方式二：手动体验

```bash
# 1. 系统检查
python check.py

# 2. 构建知识库
python main.py build

# 3. 开始对话
python main.py chat
```

#### 方式三：API 调用

```python
from rag_chain import RAGChain

# 初始化系统
rag = RAGChain()

# 提问
answer = rag.query("什么是人工智能？")
print(answer)
```

## 📁 项目结构

```
rag/
├── 🔧 核心模块
│   ├── config.py                    # 统一配置管理
│   ├── main.py                      # 主程序入口
│   ├── rag_chain.py                 # 增强RAG链
│   └── logger.py                    # 日志管理
│
├── 🗄️ 存储模块
│   ├── vector_store.py              # ChromaDB向量存储
│   ├── elasticsearch_manager.py     # ES文档存储
│   ├── mysql_manager.py             # MySQL对话存储
│   ├── session_manager.py           # Redis会话管理
│   └── context_manager.py           # 动态上下文压缩
│
├── 🔍 检索模块
│   ├── retriever.py                 # 混合检索器(ES+向量)
│   ├── embeddings.py                # BGE嵌入模型
│   └── query_expander.py            # JiebaTokenizer查询扩展
│
├── 🤖 AI模块
│   ├── llm.py                       # 通义百炼LLM
│   └── tokenizer.py                 # 中文分词器
│
├── 📄 文档处理
│   ├── document_loader.py           # 文档加载器
│   └── text_splitter.py             # 文本分割器
│
├── 🧪 测试模块
│   ├── check.py                     # 系统检查
│   ├── test_enhanced_features.py    # 完整功能测试
│   ├── test_core_enhancements.py    # 核心功能测试
│   ├── test_improvements.py         # 改进功能测试
│   └── example_usage.py             # 使用示例
│
├── 🔄 模拟模块
│   ├── mock_redis.py                # Redis模拟客户端
│   └── (ES/MySQL模拟客户端内置)
│
├── 📊 数据库
│   ├── up.sql                       # MySQL建表语句
│   └── DATABASE_SETUP.md            # 数据库设置指南
│
├── 📚 文档
│   ├── README.md                    # 项目说明(本文件)
│   ├── IMPROVEMENTS.md              # 改进功能说明
│   ├── ENHANCEMENTS_SUMMARY.md     # 增强功能总结
│   └── requirements.txt             # 依赖列表
│
└── 📁 数据目录
    ├── documents/                   # 文档存储
    ├── vectorstore/                 # ChromaDB数据
    ├── models/                      # 模型缓存
    └── logs/                        # 日志文件
```

### 🏗️ 模块说明

| 模块类型   | 核心功能         | 技术栈                        |
| ---------- | ---------------- | ----------------------------- |
| **存储层** | 多存储系统集成   | Redis + MySQL + ES + ChromaDB |
| **检索层** | 混合检索策略     | ES 粗排 + 向量精排            |
| **AI 层**  | 智能问答生成     | 通义百炼 + BGE 嵌入           |
| **处理层** | 文档和上下文处理 | JiebaTokenizer + Transformers |
| **监控层** | 系统健康检查     | 多维度监控和统计              |

## 🏗️ 技术架构

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    RAG智能问答系统                           │
├─────────────────────────────────────────────────────────────┤
│  🌐 应用层                                                  │
│  ├── main.py (CLI接口)                                     │
│  ├── rag_chain.py (核心业务逻辑)                           │
│  └── API接口 (RESTful/GraphQL)                             │
├─────────────────────────────────────────────────────────────┤
│  🧠 AI处理层                                               │
│  ├── 查询扩展 (JiebaTokenizer + 同义词)                    │
│  ├── 混合检索 (ES粗排 + 向量精排)                          │
│  ├── 上下文压缩 (Transformers摘要)                         │
│  └── 智能生成 (通义百炼LLM)                                │
├─────────────────────────────────────────────────────────────┤
│  🗄️ 存储层                                                 │
│  ├── Redis (会话+上下文)    ├── MySQL (对话持久化)         │
│  ├── Elasticsearch (文档)   └── ChromaDB (向量)            │
├─────────────────────────────────────────────────────────────┤
│  🔧 基础设施层                                              │
│  ├── 配置管理 (config.py)                                  │
│  ├── 日志系统 (logger.py)                                  │
│  ├── 监控检查 (check.py)                                   │
│  └── 模拟服务 (mock_*.py)                                  │
└─────────────────────────────────────────────────────────────┘
```

### 🔄 数据流程

```
用户查询 → 查询扩展 → ES关键词检索 → 向量精排 → 上下文融合 → LLM生成 → 结果返回
    ↓           ↓           ↓           ↓           ↓           ↓
  会话管理   同义词扩展   文档粗排    语义精排    历史压缩    智能回答
    ↓           ↓           ↓           ↓           ↓           ↓
Redis存储   JiebaTokenizer  ES索引   ChromaDB   Transformers  通义百炼
```

### 🛠️ 技术栈

| 层级           | 技术组件      | 版本要求 | 用途说明             |
| -------------- | ------------- | -------- | -------------------- |
| **AI 框架**    | LangChain     | 0.2+     | LLM 应用开发框架     |
| **LLM**        | 通义百炼      | API      | 大语言模型服务       |
| **嵌入模型**   | BGE-small-zh  | v1.5     | 中文文本向量化       |
| **向量数据库** | ChromaDB      | 0.4+     | 向量存储和检索       |
| **搜索引擎**   | Elasticsearch | 8.0+     | 文档索引和关键词检索 |
| **缓存数据库** | Redis         | 6.0+     | 会话和上下文管理     |
| **关系数据库** | MySQL         | 8.0+     | 对话数据持久化       |
| **中文分词**   | Jieba         | 0.42+    | 中文文本分词         |
| **文本摘要**   | Transformers  | 4.30+    | 上下文动态压缩       |
| **Web 框架**   | FastAPI       | 0.100+   | API 服务 (可选)      |

## 🔧 配置说明

所有配置都在 `config.py` 中统一管理：

### 嵌入模型配置

```python
class EmbeddingConfig:
    model_name: str = "BAAI/bge-small-zh-v1.5"  # BGE中文嵌入模型
    device: DeviceType = DeviceType.MPS  # Apple Silicon GPU
    max_length: int = 512  # 最大文本长度
    batch_size: int = 32  # 批处理大小
```

### ChromaDB 配置

```python
class ChromaDBConfig:
    persist_directory: str = "data/vectorstore"  # 数据持久化目录
    collection_name: str = "knowledge_base"  # 集合名称
    top_k: int = 5  # 检索返回数量
    score_threshold: float = 0.7  # 相似度阈值
```

### LLM 配置

```python
class LLMConfig:
    api_key: str = ""  # 通义百炼API密钥
    model_name: str = "qwen-max"  # 模型名称
    temperature: float = 0.7  # 采样温度
    max_tokens: int = 2048  # 最大生成token数
```

### 中文分词配置

```python
class ChineseTokenizerConfig:
    tokenizer_type: str = "jieba"  # 分词器类型: manual/jieba
    remove_stop_words: bool = True  # 是否移除停用词
    user_dict_path: str = ""  # 用户词典路径
```

### 查询扩展配置

```python
class QueryExpansionConfig:
    enable_synonyms: bool = True  # 是否启用同义词扩展
    max_synonyms_per_word: int = 2  # 每个词的最大同义词数量
    similarity_threshold: float = 0.7  # 同义词相似度阈值
    max_expansion_ratio: float = 2.0  # 最大扩展比例
```

## 📖 使用示例

### 快速测试流程

```bash
# 1. 系统检查 - 检查环境和配置
python main.py check

# 输出示例：
# 🔍 RAG系统检查报告
# 整体状态: ✅ SUCCESS
# 检查项目: 8
# 成功: 7 | 警告: 1 | 错误: 0

# 2. 测试环境
python test_imports.py

# 3. 快速测试 - 自动创建示例文档并测试
python main.py quick

# 输出示例：
# 🚀 RAG系统快速测试
# ✅ RAG系统初始化完成
# 📚 开始构建知识库
# 📄 成功加载 1 个文档片段
# 🔍 正在构建向量索引...
# ✅ 知识库构建完成
# 🧪 开始测试查询...
# 测试问题: 什么是RAG？
# 回答: RAG（Retrieval-Augmented Generation）是一种结合了检索和生成的AI技术...
```

### Python API 使用

```python
from main import RAGSystem

# 初始化系统
rag = RAGSystem()
rag.initialize()

# 构建知识库
rag.build_knowledge_base("./documents", clear_existing=True)

# 查询
answer = rag.query_knowledge_base("什么是机器学习？")
print(answer)

# 获取统计信息
stats = rag.vector_store.get_stats()
print(f"文档数量: {stats['document_count']}")
```

## 🎯 核心功能

### 🗄️ 多存储系统

#### Redis 会话管理

- **会话状态**: 实时会话状态跟踪
- **上下文缓存**: 高速上下文数据访问
- **动态压缩**: 基于 Transformers 的智能摘要
- **Token 优化**: 自动 Token 计算和优化
- **过期管理**: 自动清理过期会话

#### MySQL 数据持久化

- **对话存储**: 完整对话历史记录
- **会话统计**: 详细会话统计信息
- **用户管理**: 用户信息和权限管理
- **系统配置**: 动态系统参数配置
- **监控日志**: 系统运行状态记录

#### Elasticsearch 文档检索

- **文档索引**: 高效文档内容索引
- **关键词检索**: 精准关键词匹配
- **中文分词**: IK 分词器支持
- **搜索高亮**: 结果高亮显示
- **模糊匹配**: 智能模糊搜索

#### ChromaDB 向量存储

- **向量索引**: 高维向量快速检索
- **语义搜索**: 基于语义的相似度计算
- **持久化存储**: SQLite 底层持久化
- **批量操作**: 高效批量向量操作
- **相似度阈值**: 可配置相似度过滤

### 🔍 混合检索策略

#### ES 关键词粗排

- **快速筛选**: 基于关键词快速筛选候选文档
- **布尔查询**: 支持复杂布尔查询逻辑
- **权重调整**: 可配置字段权重
- **结果排序**: 基于 TF-IDF 评分排序

#### 向量语义精排

- **语义理解**: 深度语义相似度计算
- **重排序**: 对粗排结果进行精确重排
- **分数融合**: ES 分数与向量分数智能融合
- **阈值过滤**: 多级阈值过滤机制

### 🧠 智能上下文管理

#### 动态压缩技术

- **摘要生成**: 使用 Transformers 生成智能摘要
- **Token 计算**: 精确 Token 使用量计算
- **压缩策略**: 多种压缩策略自适应选择
- **质量保证**: 压缩质量评估和优化

#### 上下文优化

- **窗口管理**: 智能上下文窗口管理
- **历史保留**: 重要历史信息保留
- **成本控制**: API 调用成本优化
- **性能监控**: 压缩效果实时监控

### 🔧 中文优化

#### JiebaTokenizer 分词

- **精准分词**: 专业中文分词处理
- **自定义词典**: 支持领域专用词典
- **停用词过滤**: 智能停用词识别和过滤
- **词性标注**: 词性分析和标注

#### 查询扩展

- **同义词扩展**: 基于同义词库的查询扩展
- **语义扩展**: 基于语义相似度的扩展
- **扩展控制**: 可配置扩展比例和阈值
- **召回优化**: 显著提升检索召回率

### 🤖 智能问答

#### 通义百炼 LLM

- **多模型支持**: qwen-turbo, qwen-max 等
- **流式输出**: 实时流式响应
- **参数调优**: 温度、长度等参数精细调节
- **错误处理**: 完善的错误处理和重试机制

#### 生成优化

- **上下文融合**: 检索结果与历史上下文智能融合
- **Prompt 工程**: 精心设计的 Prompt 模板
- **质量控制**: 生成质量评估和优化
- **安全过滤**: 内容安全检查和过滤

### 🔍 系统监控

#### 健康检查

- **依赖检查**: 全面的环境依赖检查
- **服务状态**: 各服务连接状态监控
- **资源监控**: 系统资源使用情况
- **性能指标**: 关键性能指标监控

#### 运维支持

- **日志管理**: 分级日志记录和管理
- **错误追踪**: 详细错误信息追踪
- **性能分析**: 系统性能分析和优化建议
- **自动恢复**: 故障自动检测和恢复

## 🔍 监控和调试

### 系统检查

```bash
python main.py check
```

### 查看系统状态

```bash
python main.py status
```

### 日志配置

日志自动保存到 `data/logs/` 目录，支持不同级别：

- DEBUG: 详细调试信息
- INFO: 一般信息 (默认)
- WARNING: 警告信息
- ERROR: 错误信息

### 性能监控

```python
# 获取详细统计
stats = rag_system.vector_store.get_stats()
print(f"""
存储类型: {stats['store_type']}
文档数量: {stats['document_count']}
嵌入维度: {stats['embedding_dim']}
集合名称: {stats['collection_name']}
""")
```

## 📚 API 使用

### Python SDK

```python
from rag_chain import RAGChain
from config import defaultConfig

# 初始化RAG系统
rag = RAGChain()

# 基础查询
answer = rag.query("什么是人工智能？")
print(answer)

# 带参数查询
answer = rag.query(
    "机器学习的应用场景",
    top_k=5,                    # 检索数量
    save_to_session=True,       # 保存到会话
    use_context=True           # 使用历史上下文
)

# 流式对话
for chunk in rag.stream_chat("深度学习的发展历史"):
    print(chunk, end="", flush=True)

# 获取系统统计
stats = rag.get_stats()
print(f"存储健康状态: {stats['storage_health']}")
print(f"上下文统计: {stats['context_management']}")

# 清空历史
rag.clear_history()
```

### 高级功能

```python
# 文档管理
from document_loader import DocumentLoader
from elasticsearch_manager import ElasticsearchManager

# 加载文档
loader = DocumentLoader()
documents = loader.load_directory("./documents")

# ES文档索引
es_manager = ElasticsearchManager()
for doc in documents:
    es_manager.index_document(doc)

# 混合检索
from retriever import HybridRetrieverManager

retriever = HybridRetrieverManager(vector_store, embedding_manager)
results = retriever.retrieve(
    "人工智能应用",
    top_k=10,
    es_candidates=20,           # ES粗排候选数
    use_query_expansion=True    # 启用查询扩展
)

# 上下文管理
from context_manager import ContextManager

context_mgr = ContextManager()
context_mgr.add_message(session_id, "user", "长文本消息...")
stats = context_mgr.get_context_stats(session_id)
print(f"压缩比例: {stats['compression_ratio']:.2%}")
```

## 🚀 部署指南

### 🐳 Docker 部署

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "main.py", "chat"]
```

```yaml
# docker-compose.yml
version: "3.8"
services:
  rag-system:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DASHSCOPE_API_KEY=${DASHSCOPE_API_KEY}
    depends_on:
      - redis
      - mysql
      - elasticsearch
    volumes:
      - ./data:/app/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: rag_system
    ports:
      - "3306:3306"
    volumes:
      - ./up.sql:/docker-entrypoint-initdb.d/up.sql

  elasticsearch:
    image: elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
```

### 🖥️ 本地部署

```bash
# 1. 环境准备
python -m venv rag_env
source rag_env/bin/activate

# 2. 安装依赖
pip install -r requirements.txt

# 3. 启动服务
redis-server &
mysql.server start
elasticsearch &

# 4. 初始化数据库
mysql -u root -p < up.sql

# 5. 配置环境变量
export DASHSCOPE_API_KEY="your_api_key"

# 6. 系统检查
python check.py

# 7. 启动系统
python main.py chat
```

### ☁️ 云部署

#### AWS 部署

```bash
# 使用AWS ECS + RDS + ElastiCache
aws ecs create-cluster --cluster-name rag-cluster
aws rds create-db-instance --db-instance-identifier rag-mysql
aws elasticache create-cache-cluster --cache-cluster-id rag-redis
```

#### 阿里云部署

```bash
# 使用阿里云容器服务 + RDS + Redis
aliyun ecs CreateInstance --ImageId ubuntu_20_04_x64
aliyun rds CreateDBInstance --Engine MySQL
aliyun r-kvstore CreateInstance --InstanceType Redis
```

## 🚨 注意事项

### ⚠️ 重要提醒

1. **首次使用**: 建议先运行 `python check.py` 检查系统环境
2. **API 密钥**: 必须配置通义百炼 API 密钥才能使用 LLM 功能
3. **内存要求**: BGE 模型需要约 2GB 内存，建议 8GB+系统内存
4. **存储空间**: 模型和数据需要约 10GB 存储空间
5. **网络要求**: 首次运行需要下载模型，建议良好网络环境

### 🔧 性能优化

1. **GPU 加速**: 支持 CUDA/MPS 加速，显著提升推理速度
2. **批量处理**: 文档批量处理提升索引效率
3. **缓存策略**: 多级缓存减少重复计算
4. **连接池**: 数据库连接池提升并发性能
5. **异步处理**: 支持异步 I/O 提升响应速度

### 🛡️ 安全建议

1. **API 密钥**: 使用环境变量存储敏感信息
2. **访问控制**: 配置适当的网络访问控制
3. **数据加密**: 敏感数据传输和存储加密
4. **日志脱敏**: 日志中避免记录敏感信息
5. **定期更新**: 定期更新依赖包修复安全漏洞

## 🤝 贡献指南

我们欢迎所有形式的贡献！请遵循以下步骤：

### 🔧 开发环境设置

```bash
# 1. Fork并克隆项目
git clone https://github.com/your-username/rag-system.git
cd rag-system/rag

# 2. 创建开发环境
python -m venv dev_env
source dev_env/bin/activate

# 3. 安装开发依赖
pip install -r requirements.txt
pip install pytest black flake8 mypy

# 4. 运行测试
python -m pytest test_*.py
```

### 📝 贡献流程

1. **Fork 项目** - 点击右上角 Fork 按钮
2. **创建分支** - `git checkout -b feature/your-feature`
3. **编写代码** - 遵循项目代码规范
4. **运行测试** - 确保所有测试通过
5. **提交更改** - `git commit -m "feat: add your feature"`
6. **推送分支** - `git push origin feature/your-feature`
7. **创建 PR** - 在 GitHub 上创建 Pull Request

### 🎯 贡献方向

- 🐛 **Bug 修复**: 修复已知问题
- ✨ **新功能**: 添加新的功能特性
- 📚 **文档**: 改进文档和示例
- 🧪 **测试**: 增加测试覆盖率
- 🔧 **优化**: 性能优化和代码重构
- 🌐 **国际化**: 多语言支持

### 📋 代码规范

```bash
# 代码格式化
black rag/

# 代码检查
flake8 rag/

# 类型检查
mypy rag/
```

## 📊 项目统计

| 指标           | 数值      | 说明               |
| -------------- | --------- | ------------------ |
| **代码行数**   | 15,000+   | 包含注释和文档     |
| **模块数量**   | 20+       | 核心功能模块       |
| **测试覆盖率** | 85%+      | 单元测试和集成测试 |
| **支持语言**   | 中文/英文 | 多语言文档支持     |
| **部署方式**   | 5 种      | 本地/Docker/云部署 |

## 🏆 版本历史

### v2.0.0 (当前版本)

- ✅ 多存储系统集成 (Redis + MySQL + ES + ChromaDB)
- ✅ 混合检索策略 (ES 粗排 + 向量精排)
- ✅ 动态上下文压缩 (Transformers 摘要)
- ✅ JiebaTokenizer 中文分词优化
- ✅ 完整的系统监控和健康检查

### v1.0.0 (基础版本)

- ✅ 基础 RAG 功能
- ✅ ChromaDB 向量存储
- ✅ 通义百炼 LLM 集成
- ✅ 简单文档处理

## 🔮 未来规划

### 短期目标 (1-3 个月)

- [ ] **Web 界面**: 开发用户友好的 Web 管理界面
- [ ] **API 服务**: 提供完整的 RESTful API 服务
- [ ] **多模态支持**: 支持图片、音频等多模态数据
- [ ] **实时更新**: 支持文档实时更新和增量索引

### 中期目标 (3-6 个月)

- [ ] **分布式部署**: 支持分布式集群部署
- [ ] **多租户**: 支持多租户隔离
- [ ] **高级分析**: 提供详细的使用分析和报告
- [ ] **插件系统**: 支持第三方插件扩展

### 长期目标 (6-12 个月)

- [ ] **AI Agent**: 集成 AI Agent 能力
- [ ] **知识图谱**: 支持知识图谱构建和推理
- [ ] **联邦学习**: 支持联邦学习和隐私保护
- [ ] **边缘计算**: 支持边缘设备部署

## 📄 许可证

本项目采用 **MIT 许可证**，详见 [LICENSE](LICENSE) 文件。

```
MIT License

Copyright (c) 2024 RAG System Contributors

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.
```

## 🙏 致谢

### 🔧 核心技术

- [**LangChain**](https://github.com/langchain-ai/langchain) - 强大的 LLM 应用开发框架
- [**ChromaDB**](https://github.com/chroma-core/chroma) - 高性能向量数据库
- [**Elasticsearch**](https://github.com/elastic/elasticsearch) - 分布式搜索引擎
- [**Redis**](https://github.com/redis/redis) - 高性能内存数据库
- [**MySQL**](https://github.com/mysql/mysql-server) - 可靠的关系数据库

### 🤖 AI 模型

- [**BGE**](https://github.com/FlagOpen/FlagEmbedding) - 优秀的中文嵌入模型
- [**通义百炼**](https://dashscope.aliyun.com/) - 阿里云大语言模型服务
- [**Transformers**](https://github.com/huggingface/transformers) - Hugging Face 模型库
- [**Jieba**](https://github.com/fxsjy/jieba) - 中文分词工具

### 🛠️ 开发工具

- [**PyTorch**](https://github.com/pytorch/pytorch) - 深度学习框架
- [**FastAPI**](https://github.com/tiangolo/fastapi) - 现代 Web 框架
- [**Pydantic**](https://github.com/pydantic/pydantic) - 数据验证库
- [**Rich**](https://github.com/Textualize/rich) - 终端美化工具

### 👥 贡献者

感谢所有为项目做出贡献的开发者！

<a href="https://github.com/your-repo/rag-system/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=your-repo/rag-system" />
</a>

---

<div align="center">

### 🌟 如果这个项目对您有帮助，请给我们一个星标！

[![Star History Chart](https://api.star-history.com/svg?repos=your-repo/rag-system&type=Date)](https://star-history.com/#your-repo/rag-system&Date)

**让我们一起构建更智能的未来！** 🚀

[⬆️ 回到顶部](#-rag智能问答系统)

</div>
